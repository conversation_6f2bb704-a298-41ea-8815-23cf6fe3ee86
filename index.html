<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر بحري</title>
</head>
<body>
    <!-- حاوية المتجر -->
    <div id="my-store-107738574"></div>

    <!-- سكربت Ecwid -->
    <div>
        <script data-cfasync="false" type="text/javascript" src="https://app.ecwid.com/script.js?107738574&data_platform=code&data_date=2024-08-25" charset="utf-8"></script>
        <script type="text/javascript">
            xProductBrowser(
                "categoriesPerRow=3",
                "views=grid(20,3) list(60) table(60)",
                "categoryView=grid",
                "searchView=list",
                "id=my-store-107738574"
            );
        </script>
    </div>

    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background: rgba(255, 255, 255, 0.005) !important; /* خلفية شفافة جداً */
        }

        /* الحاوية الرئيسية للمتجر */
        #my-store-107738574 {
            background: rgba(255, 255, 255, 0.01) !important;
            backdrop-filter: blur(5px) !important;
            border-radius: 15px !important;
            padding: 20px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        /* النصوص العامة */
        #my-store-107738574,
        #my-store-107738574 * {
            color: #ffffff !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
        }

        /* العناوين */
        #my-store-107738574 h1,
        #my-store-107738574 h2,
        #my-store-107738574 h3,
        #my-store-107738574 h4,
        #my-store-107738574 h5,
        #my-store-107738574 h6 {
            color: #ffffff !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7) !important;
            font-weight: bold !important;
        }

        /* الروابط */
        #my-store-107738574 a {
            color: #87CEEB !important; /* لون أزرق فاتح يتناسب مع البحر */
            text-decoration: none !important;
            transition: all 0.3s ease !important;
        }

        #my-store-107738574 a:hover {
            color: #ffffff !important;
            text-shadow: 0 0 8px rgba(135, 206, 235, 0.8) !important;
        }

        /* الأزرار */
        #my-store-107738574 button,
        #my-store-107738574 .ecwid-btn,
        #my-store-107738574 input[type="button"],
        #my-store-107738574 input[type="submit"] {
            background: linear-gradient(135deg, rgba(30, 144, 255, 0.8), rgba(0, 100, 200, 0.8)) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
            border-radius: 25px !important;
            padding: 10px 20px !important;
            font-weight: bold !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
        }

        #my-store-107738574 button:hover,
        #my-store-107738574 .ecwid-btn:hover,
        #my-store-107738574 input[type="button"]:hover,
        #my-store-107738574 input[type="submit"]:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(135, 206, 235, 0.9)) !important;
            color: #003366 !important;
            border-color: rgba(255, 255, 255, 0.8) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
        }

        /* حقول الإدخال ومربعات البحث */
        #my-store-107738574 input[type="text"],
        #my-store-107738574 input[type="email"],
        #my-store-107738574 input[type="password"],
        #my-store-107738574 input[type="search"],
        #my-store-107738574 textarea {
            background: rgba(255, 255, 255, 0.3) !important;
            border: 2px solid rgba(255, 255, 255, 0.4) !important;
            border-radius: 10px !important;
            color: #ffffff !important;
            padding: 10px !important;
            backdrop-filter: blur(5px) !important;
        }

        #my-store-107738574 input[type="text"]:focus,
        #my-store-107738574 input[type="email"]:focus,
        #my-store-107738574 input[type="password"]:focus,
        #my-store-107738574 input[type="search"]:focus,
        #my-store-107738574 textarea:focus {
            border-color: #87CEEB !important;
            box-shadow: 0 0 10px rgba(135, 206, 235, 0.5) !important;
            outline: none !important;
        }

        /* القوائم المنسدلة */
        #my-store-107738574 select {
            background: rgba(15, 25, 40, 0.95) !important; /* لون أزرق داكن جداً */
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 10px !important;
            color: #ffffff !important;
            padding: 8px 12px !important;
            backdrop-filter: blur(5px) !important;
        }

        /* خيارات القائمة المنسدلة */
        #my-store-107738574 select option {
            background: rgba(10, 20, 35, 0.98) !important; /* لون أزرق داكن جداً */
            color: #ffffff !important;
        }

        #my-store-107738574 select:focus {
            border-color: #87CEEB !important;
            box-shadow: 0 0 10px rgba(135, 206, 235, 0.5) !important;
        }

        /* بطاقات المنتجات */
        #my-store-107738574 .ecwid-productBrowser-product,
        #my-store-107738574 .grid-product {
            background: rgba(255, 255, 255, 0.03) !important;
            border: 1px solid rgba(255, 255, 255, 0.08) !important;
            border-radius: 15px !important;
            backdrop-filter: blur(3px) !important;
            transition: all 0.3s ease !important;
            padding: 15px !important;
            margin: 10px !important;
        }

        #my-store-107738574 .ecwid-productBrowser-product:hover,
        #my-store-107738574 .grid-product:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
            border-color: rgba(135, 206, 235, 0.6) !important;
        }

        /* أسعار المنتجات */
        #my-store-107738574 .ecwid-productBrowser-price {
            color: #FFD700 !important; /* لون ذهبي للأسعار */
            font-weight: bold !important;
            font-size: 1.2em !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7) !important;
        }

        /* شريط التنقل والفئات */
        #my-store-107738574 .ecwid-productBrowser-head {
            background: rgba(0, 50, 100, 0.6) !important;
            border-radius: 10px !important;
            padding: 15px !important;
            margin-bottom: 20px !important;
        }

        /* رسائل التحميل والإشعارات */
        #my-store-107738574 .ecwid-productBrowser-loading,
        #my-store-107738574 .ecwid-productBrowser-message {
            background: rgba(255, 255, 255, 0.03) !important;
            border: 1px solid rgba(255, 255, 255, 0.08) !important;
            border-radius: 10px !important;
            color: #ffffff !important;
            padding: 20px !important;
            backdrop-filter: blur(3px) !important;
        }

        /* تحسينات إضافية للمظهر البحري */
        #my-store-107738574 .ecwid-productBrowser-category {
            border-bottom: 2px solid rgba(135, 206, 235, 0.3) !important;
        }

        /* تأثيرات الحركة */
        #my-store-107738574 * {
            transition: all 0.3s ease !important;
        }

        /* تحسين النصوص الصغيرة */
        #my-store-107738574 small,
        #my-store-107738574 .ecwid-productBrowser-subcategories {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        /* placeholder للحقول */
        #my-store-107738574 input::placeholder,
        #my-store-107738574 textarea::placeholder {
            color: rgba(255, 255, 255, 0.6) !important;
        }
    </style>
</body>
</html>
